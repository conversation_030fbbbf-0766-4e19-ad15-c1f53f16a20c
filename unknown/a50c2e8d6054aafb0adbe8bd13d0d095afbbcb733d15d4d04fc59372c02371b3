#
#  Copyright 2024 The InfiniFlow Authors. All Rights Reserved.
#
#  Licensed under the Apache License, Version 2.0 (the "License");
#  you may not use this file except in compliance with the License.
#  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
#
from abc import ABC
from agent.component.base import ComponentBase, ComponentParamBase


class IterationParam(ComponentParamBase):
    """
    Define the Iteration component parameters.
    """

    def __init__(self):
        super().__init__()
        self.delimiter = ","

    def check(self):
        self.check_empty(self.delimiter, "Delimiter")


class Iteration(ComponentBase, ABC):
    component_name = "Iteration"

    def get_start(self):
        for cid in self._canvas.components.keys():
            if self._canvas.get_component(cid)["obj"].component_name.lower() != "iterationitem":
                continue
            if self._canvas.get_component(cid)["parent_id"] == self._id:
                return self._canvas.get_component(cid)

    def _run(self, history, **kwargs):
        return self.output(allow_partial=False)[1]

