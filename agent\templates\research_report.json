{"id": 10, "title": "Research report generator", "description": "A report generator that creates a research report from a given title, in the specified target language. It generates queries from the input title, then uses these to create subtitles and sections, compiling everything into a comprehensive report.", "canvas_type": "chatbot", "dsl": {"answer": [], "components": {"Answer:WittyBottlesJog": {"downstream": [], "obj": {"component_name": "Answer", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "post_answers": [], "query": []}}, "upstream": ["Template:LegalDoorsAct"]}, "Baidu:MeanBroomsMatter": {"downstream": ["Generate:YoungClowns<PERSON><PERSON>"], "obj": {"component_name": "Baidu", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [{"component_id": "IterationItem:RudeTablesSmile", "type": "reference"}], "top_n": 10}}, "parent_id": "Iteration:BlueClothsGrab", "upstream": ["IterationItem:RudeTablesSmile"]}, "Generate:EveryCoinsStare": {"downstream": ["Generate:RedWormsDouble", "Iteration:BlueClothsGrab"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 256, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "<instruction>\n<task_description>\nGenerate a series of appropriate search engine queries to break down questions based on user inquiries\n</task_description>\n\n<examples>\n<example>\nInput: User asks how to learn programming\nOutput: programming learning methods, programming tutorials for beginners\n</example>\n\n<example>\nInput: User wants to understand latest technology trends  \nOutput: tech trends 2024, latest technology news\n</example>\n\n<example>\nInput: User seeks healthy eating advice\nOutput: healthy eating guide, balanced nutrition diet\n</example>\n</examples>\n\n<instructions>\n1. Take user's question as input.\n2. Identify relevant keywords or phrases based on the topic of user's question.\n3. Use these keywords or phrases to make search engine queries.\n4. Generate a series of appropriate search engine queries to help break down user's question.\n5. Ensure output content does not contain any xml tags.\n6. The output must be pure and conform to the <example> style without other explanations.\n7. Break down into at least 4-6 subproblems.\n8. Output is separated only by commas.\n</instructions>\n\n\ntitle: {begin@title}\nlanguage: {begin@language}\nThe output must be pure and conform to the <example> style without other explanations.\nOutput is separated only by commas.\nBreak down into at least 4-6 subproblems.\n\nOutput:", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["begin"]}, "Generate:RealLoopsVanish": {"downstream": ["Template:SpottyWaspsLose"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "In a detailed report — The report should focus on the answer to {IterationItem:OliveStatesSmoke}and nothing else.\n\n\nLanguage: {begin@language}\nContext as bellow: \n\n\"{Iteration:BlueClothsGrab}\"\n\nProvide the research report in the specified language, avoiding small talk.\nThe main content is provided in markdown format\nWrite all source urls at the end of the report in apa format. ", "query": [], "temperature": 0.1, "top_p": 0.3}}, "parent_id": "Iteration:ThreeParksChew", "upstream": ["IterationItem:OliveStatesSmoke"]}, "Generate:RedWormsDouble": {"downstream": ["Iteration:ThreeParksChew"], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "According to query: ' {Generate:EveryCoinsStare}'，for ' {begin@title}', generate 3 to 5 sub-titles.\n\n<instructions>\nPlease generate 4 subheadings for the main title following these steps:\n - 1. Carefully read the provided main title and related content\n - 2. Analyze the core theme and key information points of the main title\n - 3. Ensure the generated subheadings maintain consistency and relevance with the main title\n - 4. Each subheading should:\n        - Be concise and appropriate in length\n        - Highlight a unique angle or key point\n        - Capture readers' interest\n        - Match the overall style and tone of the article\n - 5. Between subheadings:\n        - Content should not overlap\n        - Logical order should be maintained\n        - Should collectively support the main title\n        - Use numerical sequence (1, 2, 3...) to mark each subheading\n - 6. Output format requirements:\n        - Each subheading on a separate line\n        - No XML tags included\n        - Output subheadings content only\n</instructions>\n\nlanguage: {begin@language}\nGenerate a series of appropriate sub-title to help break down ' {begin@title}'.\nBreaks down complex topics into manageable subtopics.\n\nOutput:", "query": [], "temperature": 0.1, "top_p": 0.3}}, "upstream": ["Generate:EveryCoinsStare"]}, "Generate:YoungClownsKnock": {"downstream": [], "obj": {"component_name": "Generate", "inputs": [], "output": null, "params": {"cite": false, "debug_inputs": [], "frequency_penalty": 0.7, "inputs": [], "llm_id": "deepseek-chat@DeepSeek", "max_tokens": 0, "message_history_window_size": 1, "output": null, "output_var_name": "output", "parameters": [], "presence_penalty": 0.4, "prompt": "Your goal is to provide answers based on information from the internet. \nYou must use the provided search results to find relevant online information. \nYou should never use your own knowledge to answer questions.\nPlease include relevant url sources in the end of your answers.\n{Baidu:MeanBroomsMatter}\n\n\n\n\n\nlanguage: {begin@language}\n\n\n \" {Baidu:MeanBroomsMatter}\" \n\n\n\n\nUsing the above information, answer the following question or topic: \" {IterationItem:RudeTablesSmile} \"\nin a detailed report — The report should focus on the answer to the question, should be well structured, informative, in depth, with facts and numbers if available, a minimum of 1,200 words and with markdown syntax and apa format. Write all source urls at the end of the report in apa format. You should write your report only based on the given information and nothing else.", "query": [], "temperature": 0.1, "top_p": 0.3}}, "parent_id": "Iteration:BlueClothsGrab", "upstream": ["Baidu:MeanBroomsMatter"]}, "Iteration:BlueClothsGrab": {"downstream": [], "obj": {"component_name": "Iteration", "inputs": [], "output": null, "params": {"debug_inputs": [], "delimiter": ",", "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [{"component_id": "Generate:EveryCoinsStare", "type": "reference"}]}}, "upstream": ["Generate:EveryCoinsStare"]}, "Iteration:ThreeParksChew": {"downstream": ["Template:LegalDoorsAct"], "obj": {"component_name": "Iteration", "inputs": [], "output": null, "params": {"debug_inputs": [], "delimiter": "\n", "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": [{"component_id": "Generate:RedWormsDouble", "type": "reference"}]}}, "upstream": ["Generate:RedWormsDouble"]}, "IterationItem:OliveStatesSmoke": {"downstream": ["Generate:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "obj": {"component_name": "IterationItem", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": []}}, "parent_id": "Iteration:ThreeParksChew", "upstream": []}, "IterationItem:RudeTablesSmile": {"downstream": ["Baidu:MeanBroomsMatter"], "obj": {"component_name": "IterationItem", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "query": []}}, "parent_id": "Iteration:BlueClothsGrab", "upstream": []}, "Template:LegalDoorsAct": {"downstream": ["Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>J<PERSON>"], "obj": {"component_name": "Template", "inputs": [], "output": null, "params": {"content": "<h1> {begin@title}</h1>\n\n\n\n{Iteration:ThreeParksChew}", "debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "parameters": [], "query": []}}, "upstream": ["Iteration:ThreeParksChew"]}, "Template:SpottyWaspsLose": {"downstream": [], "obj": {"component_name": "Template", "inputs": [], "output": null, "params": {"content": "<h2> {IterationItem:OliveStatesSmoke}</h2>\n<div> {Generate:RealLoopsVanish}</div>", "debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "parameters": [], "query": []}}, "parent_id": "Iteration:ThreeParksChew", "upstream": ["Generate:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "begin": {"downstream": ["Generate:EveryCoinsStare"], "obj": {"component_name": "<PERSON><PERSON>", "inputs": [], "output": null, "params": {"debug_inputs": [], "inputs": [], "message_history_window_size": 22, "output": null, "output_var_name": "output", "prologue": "", "query": [{"key": "title", "name": "Title", "optional": false, "type": "line"}, {"key": "language", "name": "Language", "optional": false, "type": "line"}]}}, "upstream": []}}, "embed_id": "", "graph": {"edges": [{"id": "reactflow__edge-Baidu:SharpHotelsNailb-Generate:RealCamerasSendb", "markerEnd": "logo", "source": "Baidu:SharpHotelsNail", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:RealCamerasSend", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "reactflow__edge-Generate:BeigeEyesFlyb-Template:ThinSnailsDreamc", "markerEnd": "logo", "source": "Generate:BeigeEyesFly", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Template:ThinSnailsDream", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "reactflow__edge-IterationItem:RudeTablesSmile-Baidu:MeanBroomsMatterc", "markerEnd": "logo", "source": "IterationItem:RudeTablesSmile", "sourceHandle": null, "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Baidu:MeanBroomsMatter", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:EveryCoinsStareb-Generate:RedWormsDoublec", "markerEnd": "logo", "source": "Generate:EveryCoinsStare", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:RedWormsDouble", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__begin-Generate:EveryCoinsStarec", "markerEnd": "logo", "source": "begin", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:EveryCoinsStare", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:RedWormsDoubleb-Iteration:ThreeParksChewc", "markerEnd": "logo", "source": "Generate:RedWormsDouble", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Iteration:ThreeParksChew", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:EveryCoinsStareb-Iteration:BlueClothsGrabc", "markerEnd": "logo", "source": "Generate:EveryCoinsStare", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Iteration:BlueClothsGrab", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Baidu:MeanBroomsMatterb-Generate:YoungClownsKnockb", "markerEnd": "logo", "source": "Baidu:MeanBroomsMatter", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:YoungClowns<PERSON><PERSON>", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__IterationItem:OliveStatesSmoke-Generate:RealLoopsVanishc", "markerEnd": "logo", "source": "IterationItem:OliveStatesSmoke", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Generate:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Generate:RealLoopsVanishb-Template:SpottyWaspsLoseb", "markerEnd": "logo", "source": "Generate:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Template:SpottyWaspsLose", "targetHandle": "b", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Iteration:ThreeParksChewb-Template:LegalDoorsActc", "markerEnd": "logo", "source": "Iteration:ThreeParksChew", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Template:LegalDoorsAct", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}, {"id": "xy-edge__Template:LegalDoorsActb-Answer:WittyBottlesJogc", "markerEnd": "logo", "source": "Template:LegalDoorsAct", "sourceHandle": "b", "style": {"stroke": "rgb(202 197 245)", "strokeWidth": 2}, "target": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>J<PERSON>", "targetHandle": "c", "type": "buttonEdge", "zIndex": 1001}], "nodes": [{"data": {"form": {"prologue": "", "query": [{"key": "title", "name": "Title", "optional": false, "type": "line"}, {"key": "language", "name": "Language", "optional": false, "type": "line"}]}, "label": "<PERSON><PERSON>", "name": "begin"}, "dragging": false, "height": 130, "id": "begin", "measured": {"height": 130, "width": 200}, "position": {"x": -231.29149905979648, "y": 95.28494230291383}, "positionAbsolute": {"x": -185.67257819905137, "y": 108.15225637884839}, "selected": false, "sourcePosition": "left", "targetPosition": "right", "type": "beginNode", "width": 200}, {"data": {"form": {}, "label": "Answer", "name": "Interact_0"}, "dragging": false, "height": 44, "id": "Answer:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>J<PERSON>", "measured": {"height": 44, "width": 200}, "position": {"x": 1458.2651570288865, "y": 164.22699667633927}, "positionAbsolute": {"x": 1462.7745767525992, "y": 231.9248108743051}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "logicNode", "width": 200}, {"data": {"form": {"delimiter": ",", "query": [{"component_id": "Generate:EveryCoinsStare", "type": "reference"}]}, "label": "Iteration", "name": "Search"}, "dragging": false, "height": 192, "id": "Iteration:BlueClothsGrab", "measured": {"height": 192, "width": 334}, "position": {"x": 432.63496522555613, "y": 228.82343789018051}, "positionAbsolute": {"x": 441.29535207641436, "y": 291.9929929170084}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 337, "width": 356}, "targetPosition": "left", "type": "group", "width": 334}, {"data": {"form": {}, "label": "IterationItem", "name": "IterationItem"}, "dragging": false, "extent": "parent", "height": 44, "id": "IterationItem:RudeTablesSmile", "measured": {"height": 44, "width": 44}, "parentId": "Iteration:BlueClothsGrab", "position": {"x": 22, "y": 10}, "positionAbsolute": {"x": -261.5, "y": -288.14062500000006}, "selected": false, "type": "iterationStartNode", "width": 44}, {"data": {"form": {"query": [{"component_id": "IterationItem:RudeTablesSmile", "type": "reference"}], "top_n": 10}, "label": "Baidu", "name": "Baidu"}, "dragging": false, "extent": "parent", "height": 64, "id": "Baidu:MeanBroomsMatter", "measured": {"height": 64, "width": 200}, "parentId": "Iteration:BlueClothsGrab", "position": {"x": 200, "y": 0}, "positionAbsolute": {"x": -83.49999999999999, "y": -298.14062500000006}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "ragNode", "width": 200}, {"data": {"form": {"delimiter": "\n", "query": [{"component_id": "Generate:RedWormsDouble", "type": "reference"}]}, "label": "Iteration", "name": "Sections"}, "dragging": false, "height": 225, "id": "Iteration:ThreeParksChew", "measured": {"height": 225, "width": 315}, "position": {"x": 888.9524716285371, "y": 75.91277516159235}, "positionAbsolute": {"x": 891.9430519048244, "y": 39.64877134989487}, "resizing": false, "selected": false, "sourcePosition": "right", "style": {"height": 438, "width": 328}, "targetPosition": "left", "type": "group", "width": 315}, {"data": {"form": {}, "label": "IterationItem", "name": "IterationItem"}, "dragging": false, "extent": "parent", "height": 44, "id": "IterationItem:OliveStatesSmoke", "measured": {"height": 44, "width": 44}, "parentId": "Iteration:ThreeParksChew", "position": {"x": 24.66038685085823, "y": 37.00025154774299}, "positionAbsolute": {"x": 780.5000000000002, "y": 432.859375}, "selected": false, "type": "iterationStartNode", "width": 44}, {"data": {"form": {"text": "It can generate a research report base on the title and language you provide."}, "label": "Note", "name": "Usage"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 168, "id": "Note:PoorMirrorsJump", "measured": {"height": 168, "width": 275}, "position": {"x": -192.4712202594548, "y": -164.26382748469516}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 275}, {"data": {"form": {"text": "LLM provides a series of search engine queries related to the proposition. Comprehensive research can be conducted through queries from different perspectives."}, "label": "Note", "name": "N-Query"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 207, "id": "Note:TwoSingersFly", "measured": {"height": 207, "width": 256}, "position": {"x": 90.71637834539166, "y": -160.7863367019141}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 256}, {"data": {"form": {"text": "LLM generates 4 subtitles for this report according to queries and title."}, "label": "Note", "name": "N-Subtitles"}, "dragHandle": ".note-drag-handle", "dragging": false, "id": "Note:SmoothAreasBet", "measured": {"height": 128, "width": 266}, "position": {"x": 431.07789651000473, "y": -161.0756093374443}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode"}, {"data": {"form": {"text": "LLM generates a report for each query based on search result of each query.\nYou could change Baidu to other search engines."}, "label": "Note", "name": "N-Search"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 168, "id": "Note:CleanTablesCamp", "measured": {"height": 168, "width": 364}, "position": {"x": 435.9578972976612, "y": 452.5021839330345}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 364}, {"data": {"form": {"text": "LLM generates 4 sub-sections for 4 subtitles based on the report of search engine result."}, "label": "Note", "name": "N-Sections"}, "dragHandle": ".note-drag-handle", "dragging": false, "height": 142, "id": "Note:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 142, "width": 336}, "position": {"x": 881.4352587545767, "y": -165.7333893115248}, "resizing": false, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "noteNode", "width": 336}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": true, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "<instruction>\n<task_description>\nGenerate a series of appropriate search engine queries to break down questions based on user inquiries\n</task_description>\n\n<examples>\n<example>\nInput: User asks how to learn programming\nOutput: programming learning methods, programming tutorials for beginners\n</example>\n\n<example>\nInput: User wants to understand latest technology trends  \nOutput: tech trends 2024, latest technology news\n</example>\n\n<example>\nInput: User seeks healthy eating advice\nOutput: healthy eating guide, balanced nutrition diet\n</example>\n</examples>\n\n<instructions>\n1. Take user's question as input.\n2. Identify relevant keywords or phrases based on the topic of user's question.\n3. Use these keywords or phrases to make search engine queries.\n4. Generate a series of appropriate search engine queries to help break down user's question.\n5. Ensure output content does not contain any xml tags.\n6. The output must be pure and conform to the <example> style without other explanations.\n7. Break down into at least 4-6 subproblems.\n8. Output is separated only by commas.\n</instructions>\n\n\ntitle: {begin@title}\nlanguage: {begin@language}\nThe output must be pure and conform to the <example> style without other explanations.\nOutput is separated only by commas.\nBreak down into at least 4-6 subproblems.\n\nOutput:", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "<PERSON><PERSON><PERSON><PERSON>"}, "dragging": false, "id": "Generate:EveryCoinsStare", "measured": {"height": 106, "width": 200}, "position": {"x": 42.60311386535324, "y": 107.45415912015176}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "According to query: ' {Generate:EveryCoinsStare}'，for ' {begin@title}', generate 3 to 5 sub-titles.\n\n<instructions>\nPlease generate 4 subheadings for the main title following these steps:\n - 1. Carefully read the provided main title and related content\n - 2. Analyze the core theme and key information points of the main title\n - 3. Ensure the generated subheadings maintain consistency and relevance with the main title\n - 4. Each subheading should:\n        - Be concise and appropriate in length\n        - Highlight a unique angle or key point\n        - Capture readers' interest\n        - Match the overall style and tone of the article\n - 5. Between subheadings:\n        - Content should not overlap\n        - Logical order should be maintained\n        - Should collectively support the main title\n        - Use numerical sequence (1, 2, 3...) to mark each subheading\n - 6. Output format requirements:\n        - Each subheading on a separate line\n        - No XML tags included\n        - Output subheadings content only\n</instructions>\n\nlanguage: {begin@language}\nGenerate a series of appropriate sub-title to help break down ' {begin@title}'.\nBreaks down complex topics into manageable subtopics.\n\nOutput:", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Subtitles"}, "dragging": false, "id": "Generate:RedWormsDouble", "measured": {"height": 106, "width": 200}, "position": {"x": 433.41522248658606, "y": 14.302437349777136}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "Your goal is to provide answers based on information from the internet. \nYou must use the provided search results to find relevant online information. \nYou should never use your own knowledge to answer questions.\nPlease include relevant url sources in the end of your answers.\n{Baidu:MeanBroomsMatter}\n\n\n\n\n\nlanguage: {begin@language}\n\n\n \" {Baidu:MeanBroomsMatter}\" \n\n\n\n\nUsing the above information, answer the following question or topic: \" {IterationItem:RudeTablesSmile} \"\nin a detailed report — The report should focus on the answer to the question, should be well structured, informative, in depth, with facts and numbers if available, a minimum of 1,200 words and with markdown syntax and apa format. Write all source urls at the end of the report in apa format. You should write your report only based on the given information and nothing else.", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "GenSearchReport"}, "dragging": false, "extent": "parent", "id": "Generate:YoungClowns<PERSON><PERSON>", "measured": {"height": 106, "width": 200}, "parentId": "Iteration:BlueClothsGrab", "position": {"x": 115.34644687476163, "y": 73.07611243293042}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"cite": false, "frequencyPenaltyEnabled": true, "frequency_penalty": 0.7, "llm_id": "deepseek-chat@DeepSeek", "maxTokensEnabled": false, "max_tokens": 256, "message_history_window_size": 1, "parameter": "Precise", "parameters": [], "presencePenaltyEnabled": true, "presence_penalty": 0.4, "prompt": "In a detailed report — The report should focus on the answer to {IterationItem:OliveStatesSmoke}and nothing else.\n\n\nLanguage: {begin@language}\nContext as bellow: \n\n\"{Iteration:BlueClothsGrab}\"\n\nProvide the research report in the specified language, avoiding small talk.\nThe main content is provided in markdown format\nWrite all source urls at the end of the report in apa format. ", "temperature": 0.1, "temperatureEnabled": true, "topPEnabled": true, "top_p": 0.3}, "label": "Generate", "name": "Subtitle-content"}, "dragging": false, "extent": "parent", "id": "Generate:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "measured": {"height": 106, "width": 200}, "parentId": "Iteration:ThreeParksChew", "position": {"x": 189.94391141062363, "y": 5.408501635610101}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "generateNode"}, {"data": {"form": {"content": "<h2> {IterationItem:OliveStatesSmoke}</h2>\n<div> {Generate:RealLoopsVanish}</div>", "parameters": []}, "label": "Template", "name": "Sub-section"}, "dragging": false, "extent": "parent", "id": "Template:SpottyWaspsLose", "measured": {"height": 76, "width": 200}, "parentId": "Iteration:ThreeParksChew", "position": {"x": 107.51010102435532, "y": 127.82322102671017}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "templateNode"}, {"data": {"form": {"content": "<h1> {begin@title}</h1>\n\n\n\n{Iteration:ThreeParksChew}", "parameters": []}, "label": "Template", "name": "Article"}, "dragging": false, "id": "Template:LegalDoorsAct", "measured": {"height": 76, "width": 200}, "position": {"x": 1209.0758608851872, "y": 149.01984563839733}, "selected": false, "sourcePosition": "right", "targetPosition": "left", "type": "templateNode"}]}, "history": [], "messages": [], "path": [], "reference": []}, "avatar": "data:image/jpeg;base64,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"}